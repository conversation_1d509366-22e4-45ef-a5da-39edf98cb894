import { useState, useEffect, useRef } from 'react';
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, ArrowRight } from 'lucide-react';

const PortfolioSection = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [isPaused, setIsPaused] = useState(false);

  const projects = [
    {
      title: "متجر إلكتروني متقدم",
      description: "منصة تجارة إلكترونية شاملة مع نظام دفع متكامل",
      image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=1170&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      tags: ["React", "Node.js", "MongoDB", "Stripe"],
      category: "تجارة إلكترونية"
    },
    {
      title: "تطبيق إدارة المشاريع",
      description: "تطبيق موبايل لإدارة المشاريع والفرق بكفاءة عالية",
      image: "https://images.unsplash.com/photo-1552664730-d307ca884978?q=80&w=1170&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      tags: ["React Native", "Firebase", "Redux", "Push Notifications"],
      category: "تطبيق موبايل"
    },
    {
      title: "موقع شركة استشارية",
      description: "موقع احترافي محسّن لمحركات البحث مع إدارة محتوى",
      image: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?q=80&w=1170&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      tags: ["WordPress", "SEO", "Responsive", "CMS"],
      category: "موقع ويب"
    },
    {
      title: "منصة تعليمية تفاعلية",
      description: "نظام إدارة تعلم مع فيديوهات تفاعلية وامتحانات",
      image: "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?auto=format&fit=crop&w=800&q=80",
      tags: ["Vue.js", "Laravel", "MySQL", "Video Streaming"],
      category: "منصة تعليمية"
    },
    {
      title: "نظام لـ جمع التبرعات ",
      description: "منصة لجمع التبرعات للمشاريع الخيرية مع تقارير مفصلة",
      image: "https://plus.unsplash.com/premium_photo-1683134261048-dfb427f8becf?q=80&w=1170&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      tags: ["Angular", "Node.js", "MongoDB", "Analytics"],
      category: "منصة تبرعات"
    },
    {
      title: "نظام إدارة العيادات",
      description: "نظام شامل لإدارة المواعيد والمرضى والفواتير",
      image: "https://plus.unsplash.com/premium_photo-1681967011376-5196197ed473?q=80&w=1230&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      tags: ["React", "Express", "PostgreSQL", "Charts"],
      category: "نظام إدارة"
    }
  ];

  // Auto-play effect
  useEffect(() => {
    if (!isPaused) {
      intervalRef.current = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % projects.length);
      }, 3000); // كل 3 ثواني
    }
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [isPaused, projects.length]);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % projects.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + projects.length) % projects.length);
  };

  return (
    <div className="container mx-auto px-4">
      <div className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
          معرض أعمالنا
        </h2>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">
          مجموعة مختارة من مشاريعنا الناجحة التي حققت أهداف عملائنا
        </p>
      </div>

      <div className="relative">
        {/* Main carousel - صورة واحدة فقط */}
        <div
          className="overflow-hidden rounded-2xl"
          onMouseEnter={() => setIsPaused(true)}
          onMouseLeave={() => setIsPaused(false)}
        >
          <div className="flex transition-transform duration-500 ease-in-out">
            <div className="w-full flex-shrink-0">
              <Card className="bg-white/5 backdrop-blur-md border border-white/10 overflow-hidden group">
                <div className="relative h-64 md:h-96 overflow-hidden">
                  <img
                    src={projects[currentIndex].image}
                    alt={projects[currentIndex].title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                    loading="lazy"
                    onError={(e) => {
                      console.log(`Failed to load image: ${projects[currentIndex].image}`);
                      e.currentTarget.src = 'https://via.placeholder.com/800x600/6B46C1/FFFFFF?text=' + encodeURIComponent(projects[currentIndex].title);
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <Badge className="mb-2 bg-purple-600/80 text-white">
                      {projects[currentIndex].category}
                    </Badge>
                    <h3 className="text-2xl font-bold text-white mb-2">{projects[currentIndex].title}</h3>
                    <p className="text-gray-200 mb-4">{projects[currentIndex].description}</p>
                    <div className="flex flex-wrap gap-2">
                      {projects[currentIndex].tags.map((tag, tagIndex) => (
                        <Badge key={tagIndex} variant="outline" className="border-white/30 text-white">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>

        {/* Navigation buttons */}
        <button
          onClick={prevSlide}
          className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/10 backdrop-blur-md rounded-full p-3 hover:bg-white/20 transition-all duration-300 z-10"
        >
          <ArrowRight className="h-6 w-6" />
        </button>
        <button
          onClick={nextSlide}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/10 backdrop-blur-md rounded-full p-3 hover:bg-white/20 transition-all duration-300 z-10"
        >
          <ArrowLeft className="h-6 w-6" />
        </button>

        {/* Dots indicator */}
        <div className="flex justify-center mt-8 space-x-2 space-x-reverse">
          {projects.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${index === currentIndex
                  ? 'bg-purple-400 w-8'
                  : 'bg-white/30 hover:bg-white/50'
                }`}
            />
          ))}
        </div>
      </div>

      {/* Projects Grid - الصور المصغرة */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 mt-12">
        {projects.map((project, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={`relative rounded-xl overflow-hidden group transition-all duration-300 aspect-square ${index === currentIndex
                ? 'ring-2 ring-purple-400 scale-105'
                : 'opacity-70 hover:opacity-100 hover:scale-105'
              }`}
          >
            <img
              src={project.image}
              alt={project.title}
              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
              loading="lazy"
              onError={(e) => {
                console.log(`Failed to load thumbnail: ${project.image}`);
                e.currentTarget.src = 'https://via.placeholder.com/200x200/6B46C1/FFFFFF?text=' + encodeURIComponent(project.title.substring(0, 10));
              }}
            />
            <div className="absolute inset-0 bg-black/40 group-hover:bg-black/20 transition-colors duration-300"></div>
            <div className="absolute inset-0 flex items-center justify-center p-2">
              <p className="text-white text-sm font-medium text-center leading-tight">{project.title}</p>
            </div>
            {/* Active indicator */}
            {index === currentIndex && (
              <div className="absolute top-2 right-2 w-3 h-3 bg-purple-400 rounded-full animate-pulse"></div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export default PortfolioSection;

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import {
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  ArrowUp
} from 'lucide-react';
import Hero from '@/components/Hero';
import ServicesSection from '@/components/ServicesSection';
import PortfolioSection from '@/components/PortfolioSection';
import TestimonialsSection from '@/components/TestimonialsSection';
import TechnologiesSection from '@/components/TechnologiesSection';
import ProcessSection from '@/components/ProcessSection';
import AnimatedBackground from '@/components/AnimatedBackground';
import WhatsAppButton from '@/components/WhatsAppButton';

const Index = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [activeSection, setActiveSection] = useState('hero');

  useEffect(() => {
    setIsLoaded(true);

    // Intersection Observer for scroll animations
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-fade-in');
            setActiveSection(entry.target.id);
          }
        });
      },
      { threshold: 0.1 }
    );

    // Observe all sections
    const sections = document.querySelectorAll('section[id]');
    sections.forEach((section) => observer.observe(section));

    return () => observer.disconnect();
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const faqs = [
    {
      question: "كم يستغرق تطوير موقع إلكتروني؟",
      answer: "يتراوح الوقت بين 2-8 أسابيع حسب تعقيد المشروع ومتطلباته. نقدم جدولاً زمنياً مفصلاً قبل البدء."
    },
    {
      question: "هل تقدمون خدمات الصيانة والدعم؟",
      answer: "نعم، نقدم خدمات صيانة ودعم فني مستمر مع ضمان أداء عالي وتحديثات دورية لضمان أمان موقعك."
    },
    {
      question: "ما هي تكلفة تطوير تطبيق موبايل؟",
      answer: "تختلف التكلفة حسب نوع التطبيق والميزات المطلوبة. نقدم استشارة مجانية لتقدير التكلفة بدقة."
    },
    {
      question: "كيف تضمنون تحسين محركات البحث SEO؟",
      answer: "نطبق أفضل ممارسات SEO من البداية، بما في ذلك تحسين السرعة، المحتوى، والبنية التقنية للموقع."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white relative overflow-x-hidden" dir="rtl">
      <AnimatedBackground />

      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-row-reverse justify-between items-center">
            <div className="text-4xl font-bold bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
              Coder
            </div>
            <div className="flex gap-4">
              <button onClick={() => scrollToSection('hero')} className="hover:text-purple-400 transition-colors">الرئيسية</button>
              <button onClick={() => scrollToSection('services')} className="hover:text-purple-400 transition-colors">خدماتنا</button>
              <button onClick={() => scrollToSection('portfolio')} className="hover:text-purple-400 transition-colors">أعمالنا</button>
              <button onClick={() => scrollToSection('testimonials')} className="hover:text-purple-400 transition-colors">آراء العملاء</button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="hero" className="relative min-h-screen flex items-center justify-center">
        <Hero />
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 relative">
        <ServicesSection />
      </section>

      {/* Technologies Section */}
      <section id="technologies" className="py-20 relative">
        <TechnologiesSection />
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 relative">
        <PortfolioSection />
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 relative">
        <TestimonialsSection />
      </section>

      {/* Process Section */}
      <section id="process" className="py-20 relative">
        <ProcessSection />
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-20 relative">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
              الأسئلة الشائعة
            </h2>
            <p className="text-xl text-gray-300">إجابات على أكثر الأسئلة شيوعاً</p>
          </div>

          <div className="max-w-3xl mx-auto">
            <Accordion type="single" collapsible className="space-y-4">
              {faqs.map((faq, index) => (
                <AccordionItem key={index} value={`item-${index}`} className="bg-white/5 backdrop-blur-md rounded-lg border border-white/10">
                  <AccordionTrigger className="text-right px-6 hover:text-purple-400">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-right px-6 text-gray-300">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black/40 backdrop-blur-md py-12 border-t border-white/10">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-2xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
                Coder
              </h3>
              <p className="text-gray-300">
                نحول أفكارك إلى حلول تقنية متطورة تساعد في نمو أعمالك وتحقيق أهدافك الرقمية.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-4">خدماتنا</h4>
              <ul className="space-y-2 text-gray-300">
                <li>تطوير المواقع</li>
                <li>تطبيقات الموبايل</li>
                <li>تحسين محركات البحث</li>
                <li>التجارة الإلكترونية</li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">روابط مهمة</h4>
              <ul className="space-y-2 text-gray-300">
                <li><button onClick={() => scrollToSection('services')} className="hover:text-purple-400">خدماتنا</button></li>
                <li><button onClick={() => scrollToSection('portfolio')} className="hover:text-purple-400">أعمالنا</button></li>
                <li><button onClick={() => scrollToSection('faq')} className="hover:text-purple-400">الأسئلة الشائعة</button></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">تواصل معنا</h4>
              <div className="space-y-2 text-gray-300">
                {/* <p>📧 <EMAIL></p> */}
                <p className='pointer-events-auto cursor-pointer hover:text-purple-400 transition-colors'
                  onClick={() => window.open(`https://wa.me/201272389525?text=${encodeURIComponent('مرحباً، أريد الاستفسار عن خدماتكم التقنية')}`, '_blank')}
                >📱 201272389525+</p>
                {/* <p>🌐 www.roya-tech.com</p> */}
              </div>
            </div>
          </div>

          <div className="border-t border-white/10 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2025  Coder. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </footer>

      {/* WhatsApp Button */}
      <WhatsAppButton />

      {/* Scroll to Top Button */}
      <button
        onClick={() => scrollToSection('hero')}
        className="fixed bottom-24 left-8 bg-gradient-to-r from-purple-600 to-cyan-600 p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
      >
        <ArrowUp className="h-6 w-6" />
      </button>
    </div>
  );
};

export default Index;

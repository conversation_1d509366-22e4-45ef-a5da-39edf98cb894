import React, { useState } from "react";

const TechnologiesSection = () => {
  const [activeTechIndex, setActiveTechIndex] = useState(0); // أول لغة مفعلة افتراضيًا

  const technologies = [
    {
      name: "JavaScript Vanilla",
      icon: <i className="devicon-javascript-plain colored"></i>,
    },
    {
      name: "Node.js",
      icon: <i className="devicon-nodejs-plain-wordmark colored"></i>,
    },
    {
      name: "React",
      icon: <i className="devicon-react-original colored"></i>,
    },
    {
      name: "Next.js",
      icon: <i className="devicon-nextjs-original-wordmark"></i>,
    },
    {
      name: "Nuxt.js",
      icon: <i className="devicon-nuxtjs-plain-wordmark colored"></i>,
    },

    {
      name: "Express js",
      icon: <i className="devicon-express-original-wordmark"></i>,
    },
    {
      name: "P<PERSON>",
      icon: <i className="devicon-php-plain colored"></i>,
    },
    {
      name: "Wordpress",
      icon: <i className="devicon-wordpress-plain"></i>,
    },
    {
      name: "SQL",
      icon: <i className="devicon-azuresqldatabase-plain colored"></i>,
    },
    {
      name: "Amazon web services",
      icon: (
        <i className="devicon-amazonwebservices-plain-wordmark colored"></i>
      ),
    },
    {
      name: "JSON",
      icon: <i className="devicon-json-plain colored"></i>,
    },
    {
      name: "XML",
      icon: <i className="devicon-xml-plain colored"></i>,
    },
    {
      name: "Flutter",
      icon: <i className="devicon-flutter-plain colored"></i>,
    },
    {
      name: "Unity engine",
      icon: <i className="devicon-unity-plain-wordmark"></i>,
    },
  ];

  return (
    <section className="py-16 overflow-hidden">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
          التقنيات التي نستخدمها
        </h2>

        <div className="flex flex-wrap justify-center gap-6 ">
          {technologies.map((tech, index) => {
            const isActive = index === activeTechIndex;
            return (
              <button
                key={index}
                onClick={() => setActiveTechIndex(index)}
                className={`flex flex-col items-center justify-center w-28 sm:w-32 md:w-36 p-4 rounded-lg transition-all 
                                    ${
                                      isActive
                                        ? "bg-gradient-to-r from-purple-800 to-cyan-800 scale-105 shadow-xl animate-[elegant-pulse_1.2s_ease-in-out_infinite]"
                                        : "bg-black/20 backdrop-blur-md border-b border-white/10 hover:shadow-md animate-pulse"
                                    }`}
              >
                <span className="text-8xl">{tech.icon}</span>
                <span className="text-sm font-medium text-center text-white">
                  {tech.name}
                </span>
              </button>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default TechnologiesSection;

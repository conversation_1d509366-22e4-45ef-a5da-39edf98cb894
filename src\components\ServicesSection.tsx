
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const ServicesSection = () => {
  const services = [
    {
      title: "تطوير المواقع الإلكترونية",
      description: "مواقع احترافية متجاوبة مع جميع الأجهزة",
      features: ["Responsive Design", "تجارة إلكترونية", "SaaS", "CMS"],
      icon: "🌐",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      title: "تطبيقات الموبايل",
      description: "تطبيقات أندرويد و iOS عالية الجودة",
      features: ["Native Apps", "Hybrid Apps", "UI/UX Design", "App Store"],
      icon: "📱",
      gradient: "from-purple-500 to-pink-500"
    },
    {
      title: "تحسين محركات البحث",
      description: "خدمات SEO متكاملة لتصدر نتائج البحث",
      features: ["On-page SEO", "Technical SEO", "Keyword Research", "Analytics"],
      icon: "🔍",
      gradient: "from-green-500 to-emerald-500"
    }
  ];

  const phoneNumber = "201272389525"; // رقم الواتساب

  const handleWhatsAppClick = (title) => {
    const message = `مرحباً، أريد عرض سعر لخدمة: ${title}`;
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };


  return (
    <div className="container mx-auto px-4">
      <div className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
          خدماتنا المتميزة
        </h2>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">
          نقدم حلول تقنية شاملة ومتطورة لتلبية جميع احتياجاتك الرقمية
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-8">
        {services.map((service, index) => (
          <Card
            key={index}
            className="bg-white/5 backdrop-blur-md border border-white/10 hover:bg-white/10 transition-all duration-500 group hover:scale-105 hover:shadow-2xl"
          >
            <CardHeader className="text-center">
              <div className="text-6xl mb-4 group-hover:scale-110 transition-transform duration-300">
                {service.icon}
              </div>
              <CardTitle className="text-xl mb-2 text-white group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-cyan-400 group-hover:bg-clip-text transition-all duration-300">
                {service.title}
              </CardTitle>
              <CardDescription className="text-gray-300">
                {service.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {service.features.map((feature, featureIndex) => (
                  <Badge
                    key={featureIndex}
                    variant="outline"
                    className="mr-2 mb-2 border-white/20 text-white hover:bg-white/10 transition-colors"
                  >
                    {feature}
                  </Badge>
                ))}
              </div>
              <div className="mt-6">
                <button 
                  onClick={() => handleWhatsAppClick(service.title)}
                  className={`w-full py-3 rounded-lg bg-gradient-to-r ${service.gradient} text-white font-semibold hover:shadow-lg transition-all duration-300 hover:scale-105`}>
                  اطلب عرض سعر
                </button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ServicesSection;

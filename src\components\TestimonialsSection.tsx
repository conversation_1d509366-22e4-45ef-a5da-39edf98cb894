
import { useState, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";

const TestimonialsSection = () => {
  const [visibleIndex, setVisibleIndex] = useState(0);

  const testimonials = [
    {
      name: "أحمد السعيد",
      position: "مدير عام شركة النور التجارية",
      content: "تعاملنا مع فريق كودر كان استثنائياً. طوروا لنا متجراً إلكترونياً احترافياً زاد مبيعاتنا بنسبة 250% خلال 6 أشهر فقط.",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=150&q=80",
      rating: 5,
      company: "شركة النور التجارية"
    },
    {
      name: "فاطمة محمد",
      position: "مؤسسة تطبيق صحتي",
      content: "فريق محترف ومتفهم لاحتياجاتنا. طوروا تطبيق صحتي بجودة عالية وفي الوقت المحدد. النتائج فاقت توقعاتنا بكثير.",
      avatar: "https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?auto=format&fit=crop&w=150&q=80",
      rating: 5,
      company: "تطبيق صحتي"
    },
    {
      name: "محمد العتيبي",
      position: "صاحب شركة البيت الذكي",
      content: "خدمات SEO ممتازة! موقعنا أصبح في المراكز الأولى في جوجل وزاد عدد العملاء الجدد بشكل كبير. أنصح بهم بقوة.",
      avatar: "https://img.freepik.com/premium-photo/arabic-man_21730-4132.jpg",
      rating: 5,
      company: "البيت الذكي"
    },
    {
      name: "سارة القحطاني",
      position: "مديرة التسويق في شركة المستقبل",
      content: "تجربتي مع فريق كودر كانت رائعة. طوروا لنا موقعاً إلكترونياً متكاملاً وسهل الاستخدام. فريقهم محترف ومتعاون.",
      avatar: "https://www.arabianbusiness.com/wp-content/uploads/sites/3/cloud/2021/09/18/9Mj6WkuQ-1-sheikha-lubna-al-qasimi-5.jpg",
      rating: 5,
      company: "شركة المستقبل"
    },
    {
      name: "يوسف البلوشي",
      position: "مؤسس منصة التعليم الذكي",
      content: "منصة التعليم الذكي أصبحت أكثر تفاعلية بفضل تطويرهم. إضافة الفيديوهات التفاعلية جعلت تجربة المستخدم أفضل بكثير.",
      avatar: "https://www.shutterstock.com/image-photo/handsome-arab-middleeastern-man-traditional-600nw-2310435237.jpg",
      rating: 5,
      company: "منصة التعليم الذكي"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setVisibleIndex((prev) => (prev + 1) % testimonials.length);
    }, 4000);
    return () => clearInterval(timer);
  }, []);

  return (
    <div className="container mx-auto px-4">
      <div className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
          آراء عملائنا
        </h2>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">
          شهادات حقيقية من عملائنا الذين حققوا النجاح معنا
        </p>
      </div>

      <div className="relative max-w-4xl mx-auto">
        <div className="overflow-hidden">
          {testimonials.map((testimonial, index) => (
            <Card
              key={index}
              className={`transition-all duration-1000 transform ${index === visibleIndex
                  ? 'opacity-100 translate-y-0 scale-100'
                  : 'opacity-0 translate-y-10 scale-95 absolute inset-0'
                } bg-white/5 backdrop-blur-md border border-white/10`}
            >
              <CardContent className="p-8 md:p-12 text-center">
                <div className="flex justify-center mb-6">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <span key={i} className="text-yellow-400 text-2xl">⭐</span>
                  ))}
                </div>

                <blockquote className="text-xl md:text-2xl text-gray-200 mb-8 leading-relaxed italic">
                  "{testimonial.content}"
                </blockquote>

                <div className="flex items-center justify-center gap-4">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    className="w-16 h-16 rounded-full object-cover border-2 border-purple-400"
                  />
                  <div className="text-right">
                    <div className="font-semibold text-white text-lg">{testimonial.name}</div>
                    <div className="text-purple-400">{testimonial.position}</div>
                    {/* <div className="text-gray-400 text-sm">{testimonial.company}</div> */}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Dots indicator */}
        <div className="flex justify-center mt-8 space-x-2 space-x-reverse">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setVisibleIndex(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${index === visibleIndex
                  ? 'bg-purple-400 w-8'
                  : 'bg-white/30 hover:bg-white/50'
                }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default TestimonialsSection;

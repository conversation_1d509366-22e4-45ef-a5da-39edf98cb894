
import { Card, CardContent } from "@/components/ui/card";

const ProcessSection = () => {
  const steps = [
    {
      number: "01",
      title: "التحليل والتخطيط",
      description: "نبدأ بفهم احتياجاتك وأهدافك لوضع استراتيجية واضحة",
      icon: "🎯",
      color: "from-purple-500 to-purple-700"
    },
    {
      number: "02",
      title: "التصميم والتطوير",
      description: "فريقنا المتخصص يحول الفكرة إلى واقع رقمي متميز",
      icon: "⚡",
      color: "from-cyan-500 to-cyan-700"
    },
    {
      number: "03",
      title: "الاختبار والمراجعة",
      description: "نختبر كل شيء بعناية لضمان الجودة والأداء الأمثل",
      icon: "🔧",
      color: "from-green-500 to-green-700"
    },
    {
      number: "04",
      title: "الإطلاق والدعم",
      description: "نطلق مشروعك ونقدم دعماً مستمراً لضمان نجاحك",
      icon: "🚀",
      color: "from-orange-500 to-orange-700"
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-12 md:mb-16">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
          خطة العمل
        </h2>
        <p className="text-lg md:text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
          منهجية عمل محترفة ومنظمة لضمان تحقيق أفضل النتائج
        </p>
      </div>

      {/* Mobile-first design with responsive layout */}
      <div className="relative">
        {/* Desktop timeline line - hidden on mobile */}
        <div className="hidden lg:block absolute left-1/2 transform -translate-x-1/2 w-1 bg-gradient-to-b from-purple-500 to-cyan-500 h-full"></div>

        {/* Mobile vertical line */}
        <div className="block lg:hidden absolute right-6 top-0 w-0.5 bg-gradient-to-b from-purple-500 to-cyan-500 h-full"></div>

        <div className="space-y-8 md:space-y-12 lg:space-y-16">
          {steps.map((step, index) => (
            <div key={index} className="relative">
              {/* Mobile Layout */}
              <div className="block lg:hidden">
                <div className="flex items-start space-x-4 space-x-reverse">
                  {/* Mobile timeline dot */}
                  <div className="flex-shrink-0 relative">
                    <div className={`w-12 h-12 bg-gradient-to-r ${step.color} rounded-full shadow-lg flex items-center justify-center relative z-10`}>
                      <span className="text-white text-sm font-bold">{step.number}</span>
                    </div>
                  </div>

                  {/* Mobile content */}
                  <div className="flex-1 min-w-0">
                    <Card className="bg-white/5 backdrop-blur-md border border-white/10 hover:bg-white/10 transition-all duration-500 group hover:scale-[1.02]">
                      <CardContent className="p-6">
                        <div className="flex items-center mb-4">
                          <div className="text-3xl group-hover:scale-110 transition-transform duration-300 mr-3">
                            {step.icon}
                          </div>
                          <h3 className="text-xl font-bold text-white group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-cyan-400 group-hover:bg-clip-text transition-all duration-300">
                            {step.title}
                          </h3>
                        </div>
                        <p className="text-gray-300 leading-relaxed text-sm">
                          {step.description}
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>

              {/* Desktop Layout */}
              <div className={`hidden lg:flex items-center ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'}`}>
                {/* Content */}
                <div className="w-full lg:w-5/12">
                  <Card className="bg-white/5 backdrop-blur-md border border-white/10 hover:bg-white/10 transition-all duration-500 group hover:scale-105">
                    <CardContent className="p-8">
                      <div className="flex items-center mb-4">
                        <div className={`bg-gradient-to-r ${step.color} text-white text-2xl font-bold rounded-full w-12 h-12 flex items-center justify-center mr-4`}>
                          {step.number}
                        </div>
                        <div className="text-4xl group-hover:scale-110 transition-transform duration-300">
                          {step.icon}
                        </div>
                      </div>
                      <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-cyan-400 group-hover:bg-clip-text transition-all duration-300">
                        {step.title}
                      </h3>
                      <p className="text-gray-300 leading-relaxed">
                        {step.description}
                      </p>
                    </CardContent>
                  </Card>
                </div>

                {/* Desktop timeline dot */}
                <div className="hidden lg:flex w-2/12 justify-center">
                  <div className={`w-8 h-8 bg-gradient-to-r ${step.color} rounded-full shadow-lg z-10 flex items-center justify-center`}>
                    <div className="w-4 h-4 bg-white rounded-full"></div>
                  </div>
                </div>

                {/* Spacer */}
                <div className="w-full lg:w-5/12"></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Call to action - improved mobile responsiveness */}
      <div className="text-center mt-12 md:mt-16">
        <div className="bg-gradient-to-r from-purple-600/20 to-cyan-600/20 backdrop-blur-md rounded-2xl p-6 md:p-8 border border-white/10 mx-auto max-w-2xl">
          <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
            مستعد لبدء رحلتك الرقمية؟
          </h3>
          <p className="text-gray-300 mb-6 text-base md:text-lg leading-relaxed">
            دعنا نساعدك في تحويل فكرتك إلى مشروع ناجح
          </p>
          <button
            className="bg-gradient-to-r from-purple-600 to-cyan-600 text-white px-6 md:px-8 py-3 md:py-4 rounded-full font-semibold text-base md:text-lg hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl w-full sm:w-auto"
            onClick={() => {
              window.open(`https://wa.me/201272389525?text=${encodeURIComponent('مرحباً، أريد الاستفسار عن خدماتكم التقنية')}`, '_blank');
            }}
          >
            ابدأ الآن 🚀
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProcessSection;
